/**
 * Admin Dashboard Real-time System
 * Provides sophisticated real-time data fetching with intelligent polling,
 * connection monitoring, and proper error handling
 */

class AdminDashboardRealtime {
    constructor() {
        this.isActive = false;
        this.intervals = {};
        this.retryAttempts = {};
        this.maxRetries = 3;
        this.baseInterval = 30000; // 30 seconds
        this.fastInterval = 10000;  // 10 seconds for critical updates
        this.slowInterval = 60000;  // 60 seconds for less critical data
        this.connectionStatus = 'connected';
        this.lastUpdateTimes = {};
        this.notificationSystem = null;

        // Initialize notification system
        this.initializeNotificationSystem();

        // Bind methods
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
        this.handleOnline = this.handleOnline.bind(this);
        this.handleOffline = this.handleOffline.bind(this);

        // Setup event listeners
        this.setupEventListeners();
    }

    /**
     * Initialize the notification system
     */
    initializeNotificationSystem() {
        this.notificationSystem = new AdminNotificationSystem();
    }

    /**
     * Setup event listeners for connection and visibility changes
     */
    setupEventListeners() {
        // Page visibility API
        document.addEventListener('visibilitychange', this.handleVisibilityChange);

        // Online/offline detection
        window.addEventListener('online', this.handleOnline);
        window.addEventListener('offline', this.handleOffline);

        // Beforeunload cleanup
        window.addEventListener('beforeunload', () => {
            this.stop();
        });
    }

    /**
     * Handle page visibility changes
     */
    handleVisibilityChange() {
        if (document.hidden) {
            this.pauseUpdates();
        } else {
            this.resumeUpdates();
        }
    }

    /**
     * Handle online status
     */
    handleOnline() {
        this.connectionStatus = 'connected';
        this.updateConnectionIndicator();
        this.resumeUpdates();
        this.notificationSystem.showToast('Connection restored', 'success');
    }

    /**
     * Handle offline status
     */
    handleOffline() {
        this.connectionStatus = 'offline';
        this.updateConnectionIndicator();
        this.pauseUpdates();
        this.notificationSystem.showToast('Connection lost', 'warning');
    }

    /**
     * Start the real-time system
     */
    start() {
        if (this.isActive) return;

        this.isActive = true;
        this.updateConnectionIndicator();

        // Start different update cycles
        this.startCriticalUpdates();
        this.startRegularUpdates();
        this.startSlowUpdates();

        console.log('Admin Dashboard Real-time system started');
    }

    /**
     * Stop the real-time system
     */
    stop() {
        this.isActive = false;

        // Clear all intervals
        Object.values(this.intervals).forEach(interval => {
            if (interval) clearInterval(interval);
        });

        this.intervals = {};
        console.log('Admin Dashboard Real-time system stopped');
    }

    /**
     * Pause updates (when page is hidden)
     */
    pauseUpdates() {
        Object.keys(this.intervals).forEach(key => {
            if (this.intervals[key]) {
                clearInterval(this.intervals[key]);
                this.intervals[key] = null;
            }
        });
    }

    /**
     * Resume updates (when page becomes visible)
     */
    resumeUpdates() {
        if (!this.isActive) return;

        // Restart all update cycles
        this.startCriticalUpdates();
        this.startRegularUpdates();
        this.startSlowUpdates();

        // Immediate refresh when resuming
        this.refreshAll();
    }

    /**
     * Start critical updates (alerts, action queue)
     */
    startCriticalUpdates() {
        if (this.intervals.critical) clearInterval(this.intervals.critical);

        this.intervals.critical = setInterval(() => {
            this.fetchAlerts();
            this.fetchActionQueue();
        }, this.fastInterval);

        // Initial fetch
        this.fetchAlerts();
        this.fetchActionQueue();
    }

    /**
     * Start regular updates (metrics, summary cards)
     */
    startRegularUpdates() {
        if (this.intervals.regular) clearInterval(this.intervals.regular);

        this.intervals.regular = setInterval(() => {
            this.fetchMetrics();
            this.fetchSummaryCards();
        }, this.baseInterval);

        // Initial fetch
        this.fetchMetrics();
        this.fetchSummaryCards();
    }

    /**
     * Start slow updates (aggregated metrics)
     */
    startSlowUpdates() {
        if (this.intervals.slow) clearInterval(this.intervals.slow);

        this.intervals.slow = setInterval(() => {
            this.fetchAggregatedMetrics();
        }, this.slowInterval);

        // Initial fetch
        this.fetchAggregatedMetrics();
    }

    /**
     * Fetch alerts with error handling
     */
    async fetchAlerts() {
        try {
            const response = await this.makeRequest('/admin/dashboard/alerts');
            if (response.success) {
                this.updateAlertsDisplay(response.data);
                this.checkForNewAlerts(response.data);
                this.resetRetryCount('alerts');
            }
        } catch (error) {
            this.handleFetchError('alerts', error);
        }
    }

    /**
     * Fetch action queue with error handling
     */
    async fetchActionQueue() {
        try {
            const response = await this.makeRequest('/admin/dashboard/action-queue');
            if (response.success) {
                this.updateActionQueueDisplay(response.data);
                this.resetRetryCount('actionQueue');
            }
        } catch (error) {
            this.handleFetchError('actionQueue', error);
        }
    }

    /**
     * Fetch metrics with error handling
     */
    async fetchMetrics() {
        try {
            const response = await this.makeRequest('/admin/dashboard/metrics');
            if (response.success) {
                this.updateMetricsDisplay(response.data);
                this.resetRetryCount('metrics');
            }
        } catch (error) {
            this.handleFetchError('metrics', error);
        }
    }

    /**
     * Fetch summary cards with error handling
     */
    async fetchSummaryCards() {
        try {
            const response = await this.makeRequest('/admin/dashboard/summary-cards');
            if (response.success) {
                this.updateSummaryCardsDisplay(response.data);
                this.resetRetryCount('summaryCards');
            }
        } catch (error) {
            this.handleFetchError('summaryCards', error);
        }
    }

    /**
     * Fetch aggregated metrics with error handling
     */
    async fetchAggregatedMetrics() {
        try {
            const response = await this.makeRequest('/admin/dashboard/aggregated-metrics');
            if (response.success) {
                this.updateAggregatedMetricsDisplay(response.data);
                this.resetRetryCount('aggregatedMetrics');
            }
        } catch (error) {
            this.handleFetchError('aggregatedMetrics', error);
        }
    }

    /**
     * Make HTTP request with proper error handling
     */
    async makeRequest(url) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    /**
     * Handle fetch errors with retry logic
     */
    handleFetchError(type, error) {
        console.error(`Error fetching ${type}:`, error);

        if (!this.retryAttempts[type]) {
            this.retryAttempts[type] = 0;
        }

        this.retryAttempts[type]++;

        if (this.retryAttempts[type] <= this.maxRetries) {
            // Exponential backoff
            const delay = Math.pow(2, this.retryAttempts[type]) * 1000;
            setTimeout(() => {
                if (this.isActive) {
                    this[`fetch${type.charAt(0).toUpperCase() + type.slice(1)}`]();
                }
            }, delay);
        } else {
            this.notificationSystem.showToast(`Failed to update ${type}`, 'error');
            this.connectionStatus = 'error';
            this.updateConnectionIndicator();
        }
    }

    /**
     * Reset retry count for successful requests
     */
    resetRetryCount(type) {
        this.retryAttempts[type] = 0;
        if (this.connectionStatus === 'error') {
            this.connectionStatus = 'connected';
            this.updateConnectionIndicator();
        }
    }

    /**
     * Refresh all data immediately
     */
    refreshAll() {
        this.fetchAlerts();
        this.fetchActionQueue();
        this.fetchMetrics();
        this.fetchSummaryCards();
        this.fetchAggregatedMetrics();
    }

    /**
     * Update connection status indicator
     */
    updateConnectionIndicator() {
        const indicator = document.getElementById('admin-summary-status');
        if (!indicator) return;

        indicator.className = 'badge fs-8';

        switch (this.connectionStatus) {
            case 'connected':
                indicator.classList.add('badge-light-success');
                indicator.innerHTML = '<i class="ki-duotone ki-check-circle fs-7 text-success me-1"><span class="path1"></span><span class="path2"></span></i>Live';
                break;
            case 'offline':
                indicator.classList.add('badge-light-warning');
                indicator.innerHTML = '<i class="ki-duotone ki-disconnect fs-7 text-warning me-1"><span class="path1"></span><span class="path2"></span></i>Offline';
                break;
            case 'error':
                indicator.classList.add('badge-light-danger');
                indicator.innerHTML = '<i class="ki-duotone ki-cross-circle fs-7 text-danger me-1"><span class="path1"></span><span class="path2"></span></i>Error';
                break;
        }
    }

    /**
     * Update alerts display
     */
    updateAlertsDisplay(alerts) {
        // Update alert count in admin summary
        const alertsIndicator = document.getElementById('alerts-indicator');
        const alertsCountElement = document.querySelector('[data-metric="alerts_count"]');

        if (alerts.length === 0) {
            if (alertsIndicator) {
                alertsIndicator.style.display = 'none';
            }
        } else {
            if (alertsIndicator) {
                alertsIndicator.style.display = 'flex';
                if (alertsCountElement) {
                    alertsCountElement.textContent = alerts.length;
                }
            }
        }

        this.lastUpdateTimes.alerts = Date.now();
    }

    /**
     * Update action queue display
     */
    updateActionQueueDisplay(actionQueue) {
        const container = document.getElementById('action-queue-container');
        if (!container) return;

        if (actionQueue.length === 0) {
            container.innerHTML = `
                <div class="text-center py-10">
                    <i class="ki-duotone ki-check-circle fs-4x text-success mb-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    <h3 class="text-gray-800 fw-bold mb-3">All caught up!</h3>
                    <p class="text-gray-400 fw-semibold fs-6">No urgent items require your attention right now.</p>
                </div>
            `;
        } else {
            let html = '';
            actionQueue.forEach(item => {
                html += this.generateActionQueueItemHtml(item);
            });
            container.innerHTML = html;
        }

        this.lastUpdateTimes.actionQueue = Date.now();
    }

    /**
     * Generate HTML for action queue item
     */
    generateActionQueueItemHtml(item) {
        let actionsHtml = '';
        item.actions.forEach(action => {
            actionsHtml += `<a href="${action.url}" class="btn ${action.class} btn-sm me-2">${action.text}</a>`;
        });

        return `
            <div class="d-flex align-items-center border border-dashed border-gray-300 rounded min-w-750px px-7 py-3 mb-5">
                <div class="w-50px">
                    <div class="badge badge-light-${item.urgency_class} fw-bold fs-8 px-2 py-1 ms-2">
                        P${item.priority}
                    </div>
                </div>
                <div class="flex-grow-1 me-2">
                    <a href="#" class="text-gray-800 text-hover-primary fs-6 fw-bold">${item.title}</a>
                    <span class="text-gray-400 fw-semibold d-block fs-7">${item.description}</span>
                    <span class="text-gray-400 fw-semibold d-block fs-8">
                        ${item.user} • ${this.formatTimeAgo(item.created_at)}
                    </span>
                </div>
                <div class="d-flex align-items-center">
                    ${actionsHtml}
                </div>
            </div>
        `;
    }

    /**
     * Update metrics display
     */
    updateMetricsDisplay(metrics) {
        // Update admin summary metrics
        this.updateMetricElement('open_tickets', metrics.open_tickets);
        this.updateMetricElement('critical_tickets', metrics.critical_tickets);
        this.updateMetricElement('high_priority_tickets', metrics.high_priority_tickets);
        this.updateMetricElement('pending_payment_amount', this.formatCurrency(metrics.pending_payment_amount));
        this.updateMetricElement('pending_payments', metrics.pending_payments);
        this.updateMetricElement('pending_senders', metrics.pending_senders);
        this.updateMetricElement('new_users_today', metrics.new_users_today);

        this.updateTimestamp();
        this.lastUpdateTimes.metrics = Date.now();
    }

    /**
     * Update summary cards display
     */
    updateSummaryCardsDisplay(summaryCards) {
        // Update summary cards if needed
        // This would involve updating the summary cards component
        this.lastUpdateTimes.summaryCards = Date.now();
    }

    /**
     * Update aggregated metrics display
     */
    updateAggregatedMetricsDisplay(metrics) {
        this.updateMetricElement('sms_last_week', this.formatNumber(metrics.sms_last_week));
        this.updateMetricElement('total_balance', this.formatCurrency(metrics.total_balance));

        this.lastUpdateTimes.aggregatedMetrics = Date.now();
    }

    /**
     * Update a specific metric element
     */
    updateMetricElement(metricName, value) {
        const element = document.querySelector(`[data-metric="${metricName}"]`);
        if (element && element.textContent !== value.toString()) {
            // Add animation class
            element.classList.add('metric-updating');
            element.textContent = value;

            // Remove animation class after animation
            setTimeout(() => {
                element.classList.remove('metric-updating');
            }, 300);
        }
    }

    /**
     * Update timestamp display
     */
    updateTimestamp() {
        const timestampElement = document.querySelector('[data-timestamp]');
        if (timestampElement) {
            const now = new Date();
            timestampElement.textContent = now.toLocaleTimeString();
            timestampElement.setAttribute('data-timestamp', now.toISOString());
        }
    }

    /**
     * Check for new alerts and show notifications
     */
    checkForNewAlerts(currentAlerts) {
        if (!this.lastUpdateTimes.alerts) return; // Skip on first load

        const previousCount = this.previousAlertsCount || 0;
        const currentCount = currentAlerts.length;

        if (currentCount > previousCount) {
            const newAlertsCount = currentCount - previousCount;
            this.notificationSystem.showToast(
                `${newAlertsCount} new critical alert${newAlertsCount > 1 ? 's' : ''}`,
                'warning'
            );
        }

        this.previousAlertsCount = currentCount;
    }

    /**
     * Format currency
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-BD', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount || 0);
    }

    /**
     * Format number
     */
    formatNumber(number) {
        return new Intl.NumberFormat().format(number || 0);
    }

    /**
     * Format time ago
     */
    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
}
