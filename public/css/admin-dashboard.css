/* Admin Dashboard Custom Styles */

/* Enhanced Summary Cards */
.summary-card {
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px !important;
    overflow: hidden;
    position: relative;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* Card Color Themes - Light Mode */
.summary-card-success {
    background: linear-gradient(135deg, #00A3FF 0%, #0077B6 100%) !important;
    color: white !important;
}

.summary-card-info {
    background: linear-gradient(135deg, #009EF7 0%, #0077B6 100%) !important;
    color: white !important;
}

.summary-card-primary {
    background: linear-gradient(135deg, #7239EA 0%, #5014D0 100%) !important;
    color: white !important;
}

.summary-card-danger {
    background: linear-gradient(135deg, #F1416C 0%, #E4002B 100%) !important;
    color: white !important;
}

.summary-card-warning {
    background: linear-gradient(135deg, #FFB800 0%, #FF8A00 100%) !important;
    color: white !important;
}

/* Text Color Overrides for Cards */
.summary-card * {
    color: white !important;
}

.summary-card .text-white {
    color: white !important;
}

.summary-card .opacity-90 {
    opacity: 0.9 !important;
}

.summary-card .opacity-80 {
    opacity: 0.8 !important;
}

/* Dark Mode Support */
[data-bs-theme="dark"] .summary-card-success {
    background: linear-gradient(135deg, #1B84FF 0%, #0066CC 100%) !important;
}

[data-bs-theme="dark"] .summary-card-info {
    background: linear-gradient(135deg, #1B84FF 0%, #0066CC 100%) !important;
}

[data-bs-theme="dark"] .summary-card-primary {
    background: linear-gradient(135deg, #8B5CF6 0%, #6D28D9 100%) !important;
}

[data-bs-theme="dark"] .summary-card-danger {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
}

[data-bs-theme="dark"] .summary-card-warning {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%) !important;
}

/* Card Enhancements */
.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.summary-card .btn-light {
    background: rgba(255, 255, 255, 0.9) !important;
    border: none !important;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    color: #1e1e2e !important;
}

.summary-card .btn-light:hover {
    background: rgba(255, 255, 255, 1) !important;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    color: #1e1e2e !important;
}

.summary-card .btn-light i {
    color: #1e1e2e !important;
}

/* Real-time Metric Updates */
.metric-updating {
    animation: metricPulse 0.3s ease-in-out;
}

@keyframes metricPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); color: #009ef7; }
    100% { transform: scale(1); }
}

/* Connection Status Indicators */
.admin-toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

/* Critical Alerts Animation */
.alert-section .alert {
    animation: slideInDown 0.5s ease-out;
    border-left: 4px solid;
}

.alert-section .alert.bg-light-danger {
    border-left-color: #F1416C;
}

.alert-section .alert.bg-light-warning {
    border-left-color: #FFC700;
}

.alert-section .alert.bg-light-info {
    border-left-color: #009EF7;
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Priority Badges */
.badge.badge-light-danger {
    background-color: rgba(241, 65, 108, 0.1);
    color: #F1416C;
    border: 1px solid rgba(241, 65, 108, 0.2);
}

.badge.badge-light-warning {
    background-color: rgba(255, 199, 0, 0.1);
    color: #FFC700;
    border: 1px solid rgba(255, 199, 0, 0.2);
}

.badge.badge-light-info {
    background-color: rgba(0, 158, 247, 0.1);
    color: #009EF7;
    border: 1px solid rgba(0, 158, 247, 0.2);
}

/* Action Queue Items */
.action-queue-item {
    transition: all 0.3s ease;
    border-radius: 8px;
}

.action-queue-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Priority Indicators */
.priority-indicator {
    width: 8px;
    height: 100%;
    border-radius: 4px;
    margin-right: 15px;
}

.priority-indicator.priority-1 {
    background-color: #F1416C;
}

.priority-indicator.priority-2 {
    background-color: #FFC700;
}

.priority-indicator.priority-3 {
    background-color: #009EF7;
}

/* Summary Cards Hover Effects */
.card-flush:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

/* Quick Actions Panel */
.quick-actions-panel .symbol:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .alert-section .alert {
        flex-direction: column;
        text-align: center;
    }

    .alert-section .alert .btn {
        margin-top: 15px;
        margin-left: 0 !important;
    }

    .action-queue-item {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .action-queue-item .d-flex.align-items-center:last-child {
        margin-top: 10px;
        width: 100%;
        justify-content: flex-start;
    }
}

/* Status Indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-indicator.critical {
    background-color: #F1416C;
    animation: pulse 2s infinite;
}

.status-indicator.warning {
    background-color: #FFC700;
}

.status-indicator.info {
    background-color: #009EF7;
}

.status-indicator.success {
    background-color: #50CD89;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(241, 65, 108, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(241, 65, 108, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(241, 65, 108, 0);
    }
}

/* Dashboard Metrics Animation */
.metric-value {
    animation: countUp 1s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Action Buttons */
.btn-action {
    min-width: 80px;
    font-size: 12px;
    padding: 6px 12px;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Empty State */
.empty-state {
    padding: 60px 20px;
    text-align: center;
}

.empty-state i {
    margin-bottom: 20px;
}

/* Refresh Button Animation */
.btn-refresh:hover i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Card Gradients */
.card-gradient-danger {
    background: linear-gradient(135deg, #F1416C 0%, #E4002B 100%);
}

.card-gradient-warning {
    background: linear-gradient(135deg, #FFC700 0%, #FF8A00 100%);
}

.card-gradient-success {
    background: linear-gradient(135deg, #50CD89 0%, #0F9D58 100%);
}

.card-gradient-info {
    background: linear-gradient(135deg, #009EF7 0%, #0077B6 100%);
}

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

/* Notification Dot */
.notification-dot {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 12px;
    height: 12px;
    background-color: #F1416C;
    border-radius: 50%;
    border: 2px solid white;
    animation: pulse 2s infinite;
}
