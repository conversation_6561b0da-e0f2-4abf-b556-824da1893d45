@extends('layouts.app')

@section('title', 'Admin Dashboard')

@push('styles')
<link href="{{ asset('css/admin-dashboard.css') }}" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">

        <!-- Balance Summary Row -->
        <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
            <!-- Balance Summary Component -->
            <x-dashboard.balance-summary
                :balance="$aggregatedMetrics['total_balance']"
                :balance-msgs="$aggregatedMetrics['total_balance_msgs']" />
        </div>

        <!-- Summary Cards Component -->
        <x-dashboard.summary-cards :cards="$summaryCards" />

        <!-- Main Content Row -->
        <div class="row g-5 g-xl-10">
            <!-- Action Queue Component -->
            <x-dashboard.action-queue :action-queue="$actionQueue" />

            <!-- Quick Actions Component -->
            <x-dashboard.quick-actions :actions="$quickActions" />
        </div>
    </div>
    <!--end::Container-->
@endsection

@push('scripts')
<script src="{{ asset('js/admin-notification-system.js') }}"></script>
<script src="{{ asset('js/admin-dashboard-realtime.js') }}"></script>
<script>
// Initialize the real-time dashboard system
let adminDashboard;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize real-time system
    adminDashboard = new AdminDashboardRealtime();
    adminDashboard.start();

    // Enhanced quick actions with loading states
    const quickActionButtons = document.querySelectorAll('[data-quick-action]');
    quickActionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
            this.disabled = true;

            // Re-enable button after 5 seconds as fallback
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 5000);
        });
    });

    // Manual refresh button for action queue
    const refreshButton = document.querySelector('[onclick="refreshActionQueue()"]');
    if (refreshButton) {
        refreshButton.onclick = function() {
            adminDashboard.fetchActionQueue();

            // Show loading state
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.add('fa-spin');
                setTimeout(() => {
                    icon.classList.remove('fa-spin');
                }, 1000);
            }
        };
    }

    // Add keyboard shortcuts for admin actions
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + R for refresh all
        if ((e.ctrlKey || e.metaKey) && e.key === 'r' && e.shiftKey) {
            e.preventDefault();
            adminDashboard.refreshAll();
            adminDashboard.notificationSystem.showInfo('Dashboard refreshed manually');
        }
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (adminDashboard) {
        adminDashboard.stop();
    }
});

// Legacy function for backward compatibility
function refreshActionQueue() {
    if (adminDashboard) {
        adminDashboard.fetchActionQueue();
    }
}
</script>
@endpush
