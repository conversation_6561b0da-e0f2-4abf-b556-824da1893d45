{{-- 
    Dashboard Critical Alerts Component
    Displays critical alerts with different types and actions
    
    Props:
    - $alerts: Array of alert objects with properties:
      - type: 'critical', 'financial', 'info'
      - icon: Icon class name
      - title: Alert title
      - message: Alert message
      - action_url: URL for action button
      - action_text: Text for action button
--}}

@props([
    'alerts' => []
])

@if(count($alerts) > 0)
    <!-- Critical Alerts Section -->
    <div class="alert-section mb-5" id="critical-alerts">
        @foreach($alerts as $alert)
            <div class="alert alert-dismissible bg-light-{{ $alert['type'] === 'critical' ? 'danger' : ($alert['type'] === 'financial' ? 'warning' : 'info') }} d-flex flex-column flex-sm-row p-5 mb-3">
                <!--begin::Icon-->
                <i class="{{ $alert['icon'] }} fs-2hx text-{{ $alert['type'] === 'critical' ? 'danger' : ($alert['type'] === 'financial' ? 'warning' : 'info') }} me-4 mb-5 mb-sm-0"></i>
                <!--end::Icon-->

                <!--begin::Wrapper-->
                <div class="d-flex flex-column pe-0 pe-sm-10">
                    <!--begin::Title-->
                    <h4 class="fw-semibold">{{ $alert['title'] }}</h4>
                    <!--end::Title-->
                    <!--begin::Content-->
                    <span>{{ $alert['message'] }}</span>
                    <!--end::Content-->
                </div>
                <!--end::Wrapper-->

                <!--begin::Action-->
                <a href="{{ $alert['action_url'] }}" class="btn btn-{{ $alert['type'] === 'critical' ? 'danger' : ($alert['type'] === 'financial' ? 'warning' : 'info') }} ms-auto">
                    {{ $alert['action_text'] }}
                </a>
                <!--end::Action-->
            </div>
        @endforeach
    </div>
@endif
