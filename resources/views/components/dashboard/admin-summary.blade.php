{{--
    Admin Summary Card Component
    Displays comprehensive administrative overview in a single card
    
    Props:
    - $metrics: Dashboard metrics data
    - $aggregatedMetrics: Aggregated SMS and cost metrics
    - $alertsCount: Number of critical alerts
--}}

@props([
    'metrics' => [],
    'aggregatedMetrics' => [],
    'alertsCount' => 0
])

<!--begin::Admin Summary Card-->
<div class="col-xl-6 mb-5">
    <!--begin::Card-->
    <div class="card card-flush border-0 h-100" data-bs-theme="light" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)">
        <!--begin::Header-->
        <div class="card-header pt-5 pb-3">
            <!--begin::Title-->
            <h3 class="card-title">
                <span class="text-white fs-3 fw-bold me-2">
                    <i class="ki-duotone ki-element-11 fs-2 text-white me-2">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                        <span class="path4"></span>
                    </i>
                    Admin Overview
                </span>
            </h3>
            <!--end::Title-->
            <!--begin::Toolbar-->
            <div class="card-toolbar">
                <span class="badge badge-light-success fs-8" id="admin-summary-status">
                    <i class="ki-duotone ki-check-circle fs-7 text-success me-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    Live
                </span>
            </div>
            <!--end::Toolbar-->
        </div>
        <!--end::Header-->
        
        <!--begin::Body-->
        <div class="card-body pt-2 pb-5" id="admin-summary-content">
            <!--begin::Stats Grid-->
            <div class="row g-3">
                <!--begin::Support Stats-->
                <div class="col-6">
                    <div class="rounded p-3" style="border: 1px dashed rgba(255, 255, 255, 0.3); background: rgba(255, 255, 255, 0.1);">
                        <div class="d-flex align-items-center mb-2">
                            <i class="ki-duotone ki-questionnaire-tablet fs-2 text-white me-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            <span class="text-white fs-6 fw-semibold">Support</span>
                        </div>
                        <div class="text-white fs-2 fw-bold" data-metric="open_tickets">{{ $metrics['open_tickets'] ?? 0 }}</div>
                        <div class="text-white opacity-75 fs-7">
                            <span data-metric="critical_tickets">{{ $metrics['critical_tickets'] ?? 0 }}</span> critical, 
                            <span data-metric="high_priority_tickets">{{ $metrics['high_priority_tickets'] ?? 0 }}</span> high
                        </div>
                    </div>
                </div>
                <!--end::Support Stats-->
                
                <!--begin::Financial Stats-->
                <div class="col-6">
                    <div class="rounded p-3" style="border: 1px dashed rgba(255, 255, 255, 0.3); background: rgba(255, 255, 255, 0.1);">
                        <div class="d-flex align-items-center mb-2">
                            <i class="ki-duotone ki-dollar fs-2 text-white me-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </i>
                            <span class="text-white fs-6 fw-semibold">Payments</span>
                        </div>
                        <div class="text-white fs-2 fw-bold">৳<span data-metric="pending_payment_amount">{{ number_format($metrics['pending_payment_amount'] ?? 0, 2) }}</span></div>
                        <div class="text-white opacity-75 fs-7">
                            <span data-metric="pending_payments">{{ $metrics['pending_payments'] ?? 0 }}</span> pending approvals
                        </div>
                    </div>
                </div>
                <!--end::Financial Stats-->
                
                <!--begin::System Stats-->
                <div class="col-6">
                    <div class="rounded p-3" style="border: 1px dashed rgba(255, 255, 255, 0.3); background: rgba(255, 255, 255, 0.1);">
                        <div class="d-flex align-items-center mb-2">
                            <i class="ki-duotone ki-send fs-2 text-white me-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            <span class="text-white fs-6 fw-semibold">Senders</span>
                        </div>
                        <div class="text-white fs-2 fw-bold" data-metric="pending_senders">{{ $metrics['pending_senders'] ?? 0 }}</div>
                        <div class="text-white opacity-75 fs-7">awaiting approval</div>
                    </div>
                </div>
                <!--end::System Stats-->
                
                <!--begin::User Stats-->
                <div class="col-6">
                    <div class="rounded p-3" style="border: 1px dashed rgba(255, 255, 255, 0.3); background: rgba(255, 255, 255, 0.1);">
                        <div class="d-flex align-items-center mb-2">
                            <i class="ki-duotone ki-people fs-2 text-white me-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                                <span class="path4"></span>
                                <span class="path5"></span>
                            </i>
                            <span class="text-white fs-6 fw-semibold">Users</span>
                        </div>
                        <div class="text-white fs-2 fw-bold" data-metric="new_users_today">{{ $metrics['new_users_today'] ?? 0 }}</div>
                        <div class="text-white opacity-75 fs-7">new today</div>
                    </div>
                </div>
                <!--end::User Stats-->
            </div>
            <!--end::Stats Grid-->
            
            <!--begin::SMS Overview-->
            <div class="separator separator-dashed border-white opacity-25 my-4"></div>
            <div class="row g-3">
                <div class="col-12">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="ki-duotone ki-sms fs-2 text-white me-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            <span class="text-white fs-6 fw-semibold">SMS Overview</span>
                        </div>
                        <span class="text-white opacity-75 fs-8" id="last-updated">
                            Updated: <span data-timestamp="{{ now()->toISOString() }}">{{ now()->format('H:i:s') }}</span>
                        </span>
                    </div>
                </div>
                
                <div class="col-6">
                    <div class="text-center">
                        <div class="text-white fs-3 fw-bold" data-metric="sms_last_week">{{ number_format($aggregatedMetrics['sms_last_week'] ?? 0) }}</div>
                        <div class="text-white opacity-75 fs-8">SMS Last Week</div>
                    </div>
                </div>
                
                <div class="col-6">
                    <div class="text-center">
                        <div class="text-white fs-3 fw-bold">৳<span data-metric="total_balance">{{ number_format($aggregatedMetrics['total_balance'] ?? 0, 2) }}</span></div>
                        <div class="text-white opacity-75 fs-8">Total Balance</div>
                    </div>
                </div>
            </div>
            <!--end::SMS Overview-->
            
            <!--begin::Alert Indicator-->
            @if($alertsCount > 0)
            <div class="separator separator-dashed border-white opacity-25 my-4"></div>
            <div class="d-flex align-items-center justify-content-center">
                <div class="badge badge-light-danger fs-7 px-3 py-2" id="alerts-indicator">
                    <i class="ki-duotone ki-warning-2 fs-6 text-danger me-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                    </i>
                    <span data-metric="alerts_count">{{ $alertsCount }}</span> Critical Alert{{ $alertsCount > 1 ? 's' : '' }}
                </div>
            </div>
            @endif
            <!--end::Alert Indicator-->
        </div>
        <!--end::Body-->
    </div>
    <!--end::Card-->
</div>
<!--end::Admin Summary Card-->
