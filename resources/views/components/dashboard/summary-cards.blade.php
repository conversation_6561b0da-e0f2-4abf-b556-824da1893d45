{{--
    Dashboard Summary Cards Component
    Displays summary statistics in gradient cards

    Props:
    - $cards: Array of summary card objects with properties:
      - value: Main value to display
      - title: Card title
      - subtitle: Card subtitle
      - color: Color theme (success, warning, danger)
      - icon: Icon class name
      - action_url: URL for action button
--}}

@props([
    'cards' => []
])

@if(count($cards) > 0)
    <!-- Summary Cards Row -->
    <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        @foreach($cards as $card)
            <div class="col-sm-6 col-xl-3">
                <!--begin::Card widget-->
                <div class="card card-flush border-0 h-lg-100 summary-card summary-card-{{ $card['color'] }}">
                    <!--begin::Header-->
                    <div class="card-header pt-6 pb-2">
                        <!--begin::Title-->
                        <div class="card-title d-flex flex-column w-100">
                            <!--begin::Icon and Title Row-->
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <div class="symbol symbol-40px symbol-circle bg-white bg-opacity-20">
                                    <i class="{{ $card['icon'] }} fs-2 text-white"></i>
                                </div>
                                @if(isset($card['trend']) && $card['trend'] !== 'stable')
                                    <span class="badge badge-light-{{ $card['color'] === 'danger' ? 'danger' : 'success' }} fs-8">
                                        <i class="ki-duotone ki-arrow-{{ $card['trend'] === 'up' ? 'up' : 'down' }} fs-7"></i>
                                        {{ ucfirst($card['trend']) }}
                                    </span>
                                @endif
                            </div>
                            <!--end::Icon and Title Row-->
                            <!--begin::Amount-->
                            <span class="fs-2hx fw-bold text-white me-2 lh-1 ls-n2 mb-2">{{ $card['value'] }}</span>
                            <!--end::Amount-->
                            <!--begin::Title-->
                            <span class="text-white opacity-90 fw-bold fs-5 mb-1">{{ $card['title'] }}</span>
                            <!--end::Title-->
                        </div>
                        <!--end::Title-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Card body-->
                    <div class="card-body d-flex flex-column justify-content-end pt-0 pb-6">
                        <!--begin::Subtitle-->
                        <div class="mb-4">
                            <span class="fs-6 fw-semibold text-white opacity-80 lh-1">{{ $card['subtitle'] }}</span>
                        </div>
                        <!--end::Subtitle-->

                        <!--begin::Separator-->
                        <div class="separator separator-dashed border-white opacity-25 mb-4"></div>
                        <!--end::Separator-->

                        <!--begin::Action Center-->
                        <div class="text-center">
                            <a href="{{ $card['action_url'] }}" class="btn btn-sm btn-light btn-active-light-primary text-dark fw-bold px-4 py-2 rounded-pill shadow-sm">
                                <i class="ki-duotone ki-eye fs-6 me-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                                View Details
                            </a>
                        </div>
                        <!--end::Action Center-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Card widget-->
            </div>
        @endforeach
    </div>
@endif
