{{-- 
    Dashboard Summary Cards Component
    Displays summary statistics in gradient cards
    
    Props:
    - $cards: Array of summary card objects with properties:
      - value: Main value to display
      - title: Card title
      - subtitle: Card subtitle
      - color: Color theme (success, warning, danger)
      - icon: Icon class name
      - action_url: URL for action button
--}}

@props([
    'cards' => []
])

@if(count($cards) > 0)
    <!-- Summary Cards Row -->
    <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
        @foreach($cards as $card)
            <div class="col-sm-6 col-xl-3">
                <!--begin::Card widget-->
                <div class="card card-flush border-0 h-lg-100" data-bs-theme="light" style="background: linear-gradient(112.14deg, {{ $card['color'] === 'success' ? '#00A3FF 0%, #0077B6 100%' : ($card['color'] === 'warning' ? '#FFB800 0%, #FF8A00 100%' : '#F1416C 0%, #E4002B 100%') }})">
                    <!--begin::Header-->
                    <div class="card-header pt-5">
                        <!--begin::Title-->
                        <div class="card-title d-flex flex-column">
                            <!--begin::Amount-->
                            <span class="fs-2hx fw-bold text-white me-2 lh-1 ls-n2">{{ $card['value'] }}</span>
                            <!--end::Amount-->
                            <!--begin::Subtitle-->
                            <span class="text-white opacity-75 pt-1 fw-semibold fs-6">{{ $card['title'] }}</span>
                            <!--end::Subtitle-->
                        </div>
                        <!--end::Title-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Card body-->
                    <div class="card-body d-flex flex-column justify-content-end pe-0">
                        <!--begin::Title-->
                        <span class="fs-6 fw-bolder text-white opacity-75 pb-1">{{ $card['subtitle'] }}</span>
                        <!--end::Title-->
                        <!--begin::Progress-->
                        <div class="d-flex align-items-center">
                            <div class="border border-white border-dashed rounded min-w-40px py-3 px-4 me-6 mb-3">
                                <!--begin::Icon-->
                                <i class="{{ $card['icon'] }} fs-2 text-white"></i>
                                <!--end::Icon-->
                            </div>
                            <!--begin::Action-->
                            <a href="{{ $card['action_url'] }}" class="btn btn-sm btn-light text-dark fw-bold ms-auto mb-3">View Details</a>
                            <!--end::Action-->
                        </div>
                        <!--end::Progress-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Card widget-->
            </div>
        @endforeach
    </div>
@endif
