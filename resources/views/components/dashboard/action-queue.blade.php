{{-- 
    Dashboard Action Queue Component
    Displays priority action items requiring immediate attention
    
    Props:
    - $actionQueue: Array of action items with properties:
      - priority: Priority number (1-5)
      - urgency_class: CSS class for urgency (danger, warning, info)
      - title: Action title
      - description: Action description
      - user: User who created the item
      - created_at: Creation timestamp
      - actions: Array of action buttons with url, class, text
--}}

@props([
    'actionQueue' => []
])

<!-- Action Queue (Left Column) -->
<div class="col-xl-8">
    <!--begin::Card-->
    <div class="card card-flush h-lg-100">
        <!--begin::Header-->
        <div class="card-header pt-5">
            <!--begin::Title-->
            <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">Priority Action Queue</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">Items requiring immediate attention</span>
            </h3>
            <!--end::Title-->
            <!--begin::Toolbar-->
            <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light-primary" onclick="refreshActionQueue()">
                    <i class="ki-duotone ki-arrows-circle fs-2">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    Refresh
                </button>
            </div>
            <!--end::Toolbar-->
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body pt-5" id="action-queue-container">
            @if(count($actionQueue) > 0)
                @foreach($actionQueue as $item)
                    <div class="d-flex align-items-center border border-dashed border-gray-300 rounded min-w-750px px-7 py-3 mb-5">
                        <!--begin::Priority indicator-->
                        <div class="w-50px">
                            <div class="badge badge-light-{{ $item['urgency_class'] }} fw-bold fs-8 px-2 py-1 ms-2">
                                P{{ $item['priority'] }}
                            </div>
                        </div>
                        <!--end::Priority indicator-->

                        <!--begin::Info-->
                        <div class="flex-grow-1 me-2">
                            <!--begin::Title-->
                            <a href="#" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{ $item['title'] }}</a>
                            <!--end::Title-->
                            <!--begin::Description-->
                            <span class="text-gray-400 fw-semibold d-block fs-7">{{ $item['description'] }}</span>
                            <!--end::Description-->
                            <!--begin::User and time-->
                            <span class="text-gray-400 fw-semibold d-block fs-8">
                                {{ $item['user'] }} • {{ $item['created_at']->diffForHumans() }}
                            </span>
                            <!--end::User and time-->
                        </div>
                        <!--end::Info-->

                        <!--begin::Actions-->
                        <div class="d-flex align-items-center">
                            @foreach($item['actions'] as $action)
                                <a href="{{ $action['url'] }}" class="btn {{ $action['class'] }} btn-sm me-2">
                                    {{ $action['text'] }}
                                </a>
                            @endforeach
                        </div>
                        <!--end::Actions-->
                    </div>
                @endforeach
            @else
                <div class="text-center py-10">
                    <i class="ki-duotone ki-check-circle fs-4x text-success mb-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    <h3 class="text-gray-800 fw-bold mb-3">All caught up!</h3>
                    <p class="text-gray-400 fw-semibold fs-6">No urgent items require your attention right now.</p>
                </div>
            @endif
        </div>
        <!--end::Body-->
    </div>
    <!--end::Card-->
</div>
