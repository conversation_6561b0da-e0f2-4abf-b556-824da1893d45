# Dashboard Components Guide

## Overview
The dashboard system has been refactored into reusable Blade components to improve maintainability and consistency across different dashboard views.

## Component Structure

### 1. Metrics Cards Component
**Location**: `resources/views/components/dashboard/metrics-cards.blade.php`

**Purpose**: Displays SMS and cost metrics in a responsive card layout

**Props**:
- `smsLastWeek` (int): Number of SMS sent last week
- `costLastWeek` (float): Cost of SMS last week
- `smsInMonth` (int): Number of SMS sent in specified month
- `costInMonth` (float): Cost of SMS in specified month
- `monthName` (string): Name of the month for display

**Usage**:
```blade
<x-dashboard.metrics-cards 
    :sms-last-week="$sms_last_week"
    :cost-last-week="$cost_last_week"
    :sms-in-month="$sms_in_last_month"
    :cost-in-month="$cost_in_last_month"
    :month-name="$last_month_name"
/>
```

### 2. Balance Summary Component
**Location**: `resources/views/components/dashboard/balance-summary.blade.php`

**Purpose**: Displays balance information in a purple gradient card

**Props**:
- `balance` (float): Current balance amount
- `balanceMsgs` (int): Balance converted to message count
- `showChart` (bool): Whether to show the chart (default: true)

**Usage**:
```blade
<x-dashboard.balance-summary 
    :balance="$balance"
    :balance-msgs="$balance_msgs"
    :show-chart="true"
/>
```

### 3. Critical Alerts Component
**Location**: `resources/views/components/dashboard/critical-alerts.blade.php`

**Purpose**: Displays critical alerts with different types and actions

**Props**:
- `alerts` (array): Array of alert objects with properties:
  - `type`: 'critical', 'financial', 'info'
  - `icon`: Icon class name
  - `title`: Alert title
  - `message`: Alert message
  - `action_url`: URL for action button
  - `action_text`: Text for action button

**Usage**:
```blade
<x-dashboard.critical-alerts :alerts="$criticalAlerts" />
```

### 4. Action Queue Component
**Location**: `resources/views/components/dashboard/action-queue.blade.php`

**Purpose**: Displays priority action items requiring immediate attention

**Props**:
- `actionQueue` (array): Array of action items with priority indicators

**Usage**:
```blade
<x-dashboard.action-queue :action-queue="$actionQueue" />
```

### 5. Quick Actions Component
**Location**: `resources/views/components/dashboard/quick-actions.blade.php`

**Purpose**: Displays common admin tasks with icons and descriptions

**Props**:
- `actions` (array): Array of quick action items

**Usage**:
```blade
<x-dashboard.quick-actions :actions="$quickActions" />
```

### 6. Summary Cards Component
**Location**: `resources/views/components/dashboard/summary-cards.blade.php`

**Purpose**: Displays summary statistics in gradient cards

**Props**:
- `cards` (array): Array of summary card objects

**Usage**:
```blade
<x-dashboard.summary-cards :cards="$summaryCards" />
```

## Dashboard Views

### Admin Dashboard
**Location**: `resources/views/admin/dashboard/index.blade.php`

**Features**:
- Aggregated metrics for all users
- Critical alerts
- Priority action queue
- Quick actions panel
- Summary cards

### User Dashboard
**Location**: `resources/views/dashboard.blade.php`

**Features**:
- User-specific metrics
- Balance summary
- Simplified layout

## Routing Configuration

### Main Dashboard Route
- **URL**: `/`
- **Logic**: Automatically redirects admin users to admin dashboard, regular users to user dashboard
- **Controller**: Dynamic based on user role

### Admin Dashboard Route
- **URL**: `/admin/dashboard`
- **Controller**: `AdminDashboardController@index`
- **Middleware**: `auth`, `verified`, role-based access control

## Data Sources

### Aggregated Metrics (Admin)
- SMS counts and costs across all companies
- Total balance across all companies
- Real-time calculations from Message and Company models

### User Metrics (Regular Users)
- Company-specific SMS counts and costs
- Individual company balance
- Tenant-specific data filtering

## Benefits of Component-Based Architecture

1. **Reusability**: Components can be used across different dashboard views
2. **Maintainability**: Changes to UI elements only need to be made in one place
3. **Consistency**: Ensures uniform styling and behavior across dashboards
4. **Testability**: Components can be tested independently
5. **Flexibility**: Easy to add new dashboard types or modify existing ones

## Future Enhancements

1. **Component Variants**: Add different styling variants for components
2. **Interactive Components**: Add AJAX-powered real-time updates
3. **Customizable Dashboards**: Allow users to configure which components to display
4. **Performance Optimization**: Implement caching for component data
5. **Mobile Responsiveness**: Enhance mobile layouts for components
